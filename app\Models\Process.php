<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Enums\ProcessVersionStatus;

class Process extends Model
{
    use HasUuids;
    
    protected $table = 'process';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'description',
        'process_group_id',
        'scope_use',
        'followers',
        'process_manager',
        'job_manager',
        'status',
        'tenant_id',
        'create_by',
    ];
    
    protected $casts = [
        'scope_use' => 'array',
        'followers' => 'array',
        'process_manager' => 'array',
        'job_manager' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function processVersions()
    {
        return $this->hasMany('App\Models\ProcessVersion', 'process_id', 'id');
    }

    public function processVersionActive()
    {
        return $this->hasOne('App\Models\ProcessVersion', 'process_id', 'id')->where('is_active', ProcessVersionStatus::TRUE->value);
    }

    public function processGroup()
    {
        return $this->belongsTo(ProcessGroup::class, 'process_group_id', 'id');
    }
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'create_by', 'id');
    }
}
