<template>
    WorkflowUpdateSetting
    <loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from 'vue';
import Loading from '@/views/loading/Loading.vue'
import useWorkflow from '@/composables/workflow';
import { useRoute } from 'vue-router';

export default defineComponent({
    name: 'WorkflowUpdateSetting',

    components: {
        Loading,
    },

    setup() {
        const route = useRoute();

        const state = reactive({
            flowTransitions: [] as any,
		});

        const { setIsLoading, getFlowTransitions } = useWorkflow();

        onMounted( async () => {
            let result = await getFlowTransitions(route.params.id);
            console.log(result);
            if (result.status === 'success') {
                state.flowTransitions = result.data_flow_transitions;
            }
        });

        return {
            setIsLoading,
            state,
        };
    },
});
</script>
